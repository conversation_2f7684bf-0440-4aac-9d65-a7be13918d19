import { ReactNode, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { useLanguageSync } from '@/hooks/use-language-sync';

interface AppWrapperProps {
  children: ReactNode;
}

const AppWrapper = ({ children }: AppWrapperProps) => {
  const { i18n } = useTranslation();
  const location = useLocation();

  // 使用useLanguageSync钩子来处理语言同步
  useLanguageSync('AppWrapper');

  // 获取当前语言
  const currentLang = i18n.language || 'en';

  // 根据当前语言获取标题和描述
  let title = '';
  let description = '';

  switch (currentLang) {
    case 'es':
      title = 'Convertidor de Texto - Herramienta Moderna de Conversión de Texto';
      description = 'Convertidor de Texto es una potente herramienta de conversión de texto que te ayuda a transformar texto a diferentes formatos como mayúsculas, minúsculas, tipo título y más. Compatible con varios idiomas.';
      break;
    case 'fr':
      title = 'Convertisseur de Texte - Outil Moderne de Conversion de Texte';
      description = 'Convertisseur de Texte est un puissant outil de conversion de texte qui vous aide à transformer le texte en différentes casses comme majuscules, minuscules, casse de titre et plus. Prend en charge plusieurs langues.';
      break;
    case 'de':
      title = 'Textumwandler - Modernes Textumwandlungstool';
      description = 'Textumwandler ist ein leistungsstarkes Text-Umwandlungstool, das Ihnen hilft, Text in Großbuchstaben, Kleinbuchstaben, Titelfall und mehr umzuwandeln. Unterstützt mehrere Sprachen.';
      break;
    case 'zh':
      title = '文本转换器 - 现代文本大小写转换工具';
      description = '文本转换器是一个强大的文本转换工具，可帮助您将文本转换为大写、小写、标题大小写等不同格式。支持多种语言。';
      break;
    default:
      title = 'Text Case Converter - Modern Text Case Conversion Tool';
      description = 'Text Case Converter is a powerful text conversion tool that helps you transform text to different cases such as uppercase, lowercase, title case and more. Supports multiple languages.';
  }

  return (
    <>
      <Helmet>
        <html lang={currentLang} />
        <title>{title}</title>
        <meta name="description" content={description} />
        <meta property="og:title" content={title} />
        <meta property="og:description" content={description} />
        <meta property="og:locale" content={currentLang} />
        <meta name="twitter:title" content={title} />
        <meta name="twitter:description" content={description} />
        {/* 不在这里添加 canonical 和 hreflang 标签，这些将由 vite.config.ts 中的脚本添加 */}
      </Helmet>
      {children}
    </>
  );
};

export default AppWrapper;
